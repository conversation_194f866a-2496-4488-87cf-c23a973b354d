package httpdumper

import (
	"bufio"
	"context"
	"errors"
	"io"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/google/gopacket"
	"github.com/google/gopacket/tcpassembly"
)

// httpStreamFactory HTTP流工厂，基于TCP流工厂
type httpStreamFactory struct {
	tcpFactory *tcpStreamFactory
}

// HttpStream HTTP流接口，对外导出
type HttpStream interface {
	// 获取流的基本信息
	GetID() string
	GetNetTransport() (gopacket.Flow, gopacket.Flow)

	// 流控制
	Close()
}

// httpStream HTTP流的具体实现
type httpStream struct {
	tcpStream *tcpStream // 底层TCP流
	factory   *httpStreamFactory
}

// httpHandler HTTP处理器实现
type httpHandler struct {
	notifier Notifier
}

// GetID 获取流ID
func (h *httpStream) GetID() string {
	return h.tcpStream.id
}

// GetNetTransport 获取网络和传输层信息
func (h *httpStream) GetNetTransport() (gopacket.Flow, gopacket.Flow) {
	return h.tcpStream.GetNetTransport()
}

// Close 关闭流
func (h *httpStream) Close() {
	h.tcpStream.ReassemblyComplete()
}

// ReassemblyComplete implements tcpassembly.Stream's ReassemblyComplete function.
// 使用 sync.Once 来确保底层的 ReaderStream.ReassemblyComplete 只被调用一次。
func (r *httpStream) ReassemblyComplete() {
	r.closeOnce.Do(func() {
		r.ReaderStream.ReassemblyComplete()
	})
}

// Reassembled implements tcpassembly.Stream's Reassembled function.
func (r *httpStream) Reassembled(reassembly []tcpassembly.Reassembly) {
	if r.state.discard.Load() {
		return
	}

	//for _, pkt := range reassembly {
	//	log.Printf("reassembled: %s:%s -> %s:%s, %d bytes, skip=%v, start=%v, end=%v\n",
	//		r.net.Src(), r.transport.Src(), r.net.Dst(), r.transport.Dst(), len(pkt.Bytes), pkt.Skip, pkt.Start, pkt.End)
	//}

	// 检查第一个包
	if r.isFirstPkt && len(reassembly) > 0 && len(reassembly[0].Bytes) > 0 {
		r.isFirstPkt = false
		firstData := reassembly[0].Bytes

		if bytes.HasPrefix(firstData, []byte("HTTP/")) {
			r.requestOrResponse = RequestOrResponseResponse
		} else if bytes.HasPrefix(firstData, []byte("GET ")) ||
			bytes.HasPrefix(firstData, []byte("HEAD ")) ||
			bytes.HasPrefix(firstData, []byte("POST ")) {
			r.requestOrResponse = RequestOrResponseRequest
		} else {
			r.requestOrResponse = RequestOrResponseError
			// 不再进行处理
			if r.Verbose {
				log.Println("not http protocol:", r.id)
			}
			r.state.discard.Store(true)
			r.ReassemblyComplete()
			return
		}
	}
	r.ReaderStream.Reassembled(reassembly)
}

func (s *httpStream) readRequest(buf *bufio.Reader) error {
	req, err := http.ReadRequest(buf)
	if err != nil {
		return err
	}

	newReq := NewRequest(req.Clone(context.Background()), s.net, s.transport)
	s.state.appendRequest(newReq)

	body, _ := io.ReadAll(req.Body)
	req.Body.Close()

	newReq.SetBody(body)

	s.factory.notifier.OnRequest(newReq)
	return nil
}

func (s *httpStream) readResponse(buf *bufio.Reader) error {
	_, err := buf.Peek(1)
	if err != nil {
		return err
	}

	req := s.state.getLastRequest()
	var rawReq *http.Request
	if req != nil {
		rawReq = req.Request
		i := 0
		for !req.processedBody && i < 10 {
			time.Sleep(time.Millisecond * 100)
			i++
		}
	}

	resp, err := http.ReadResponse(buf, rawReq)
	if err != nil {
		return err
	}
	newResp := NewResponse(req, resp, s.net, s.transport)

	body, _ := io.ReadAll(resp.Body)
	resp.Body.Close()
	newResp.SetBody(body)

	s.factory.notifier.OnResponse(newResp)
	return nil
}

func (s *httpStream) run() {
	defer func() {
		if s.Verbose {
			log.Println("out:", s.id)
		}
		s.factory.wg.Done()
	}()

	buf := bufio.NewReader(s)
	_, err := buf.Peek(1) // 必须读一次，确保s.requestOrResponse被设置

	if err != nil {
		if errors.Is(err, io.EOF) || errors.Is(err, io.ErrUnexpectedEOF) {
			return
		}
		log.Println("buf peek failed:", err)
		return
	}

_out:
	for {
		switch s.requestOrResponse {
		case RequestOrResponseRequest:
			err := s.readRequest(buf)
			if err != nil {
				if errors.Is(err, io.EOF) || errors.Is(err, io.ErrUnexpectedEOF) {
					return
				}
				log.Println("request buf read failed:", err)
				break _out
			}
		case RequestOrResponseResponse:
			err := s.readResponse(buf)
			if err != nil {
				if errors.Is(err, io.EOF) || errors.Is(err, io.ErrUnexpectedEOF) {
					return
				}
				log.Println("response buf read failed:", err)
				break _out
			}
		default:
			s.ReassemblyComplete()
		}
	}
}

// createConnectionKey 创建一个不区分流向的唯一连接标识
func createConnectionKey(netFlow, tcpFlow gopacket.Flow) string {
	srcIP, dstIP := netFlow.Endpoints()
	srcPort, dstPort := tcpFlow.Endpoints()
	if srcIP.String() > dstIP.String() || (srcIP.String() == dstIP.String() && srcPort.String() > dstPort.String()) {
		srcIP, dstIP = dstIP, srcIP
		srcPort, dstPort = dstPort, srcPort
	}
	return fmt.Sprintf("%s:%s-%s:%s", srcIP, srcPort, dstIP, dstPort)
}

func (f *httpStreamFactory) getHttpStream(net, transport gopacket.Flow) *httpStream {
	id := createConnectionKey(net, transport)

	// 设置共享状态
	state, loaded := f.m.LoadOrStore(net.String()+":"+transport.String(), &tcpState{})
	f.m.LoadOrStore(net.Reverse().String()+":"+transport.Reverse().String(), state)

	if !loaded {
		f.notifier.OnTcpSession(id, net, transport)
	}

	return &httpStream{
		id:           net.String() + ":" + transport.String(),
		ReaderStream: tcpreader.NewReaderStream(),
		factory:      f,
		net:          net,
		transport:    transport,
		state:        state.(*tcpState),
		isFirstPkt:   true,
		Verbose:      f.Verbose,
	}
}

// New 方法在检测到新的 TCP 流时被调用
// 这是关键的修改点：我们在这里判断流的方向
func (f *httpStreamFactory) New(net, transport gopacket.Flow) tcpassembly.Stream {
	s := f.getHttpStream(net, transport)
	f.wg.Add(1)
	go s.run()

	return s
}
