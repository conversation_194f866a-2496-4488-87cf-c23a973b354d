package httpdumper

import (
	"bufio"
	"bytes"
	"fmt"
	"log"
	"sync"
	"sync/atomic"

	"github.com/google/gopacket"
	"github.com/google/gopacket/tcpassembly"
	"github.com/google/gopacket/tcpassembly/tcpreader"
)

// tcpState 两端共享的状态信息
type tcpState struct {
	mutex    sync.Mutex
	discard  atomic.Bool
	requests []*Request // 请求列表
	reqIndex int        // 当前读取的偏移
}

func (ts *tcpState) appendRequest(req *Request) {
	ts.mutex.Lock()
	defer ts.mutex.Unlock()
	ts.requests = append(ts.requests, req)
}

func (ts *tcpState) getLastRequest() *Request {
	ts.mutex.Lock()
	defer ts.mutex.Unlock()

	// 收到了response但是还没有request
	if len(ts.requests) < ts.reqIndex+1 {
		return nil
	}

	req := ts.requests[ts.reqIndex]
	ts.reqIndex++
	return req
}

// RequestOrResponse 请求或响应类型
type RequestOrResponse int

const (
	RequestOrResponseWait RequestOrResponse = iota
	RequestOrResponseRequest
	RequestOrResponseResponse
	RequestOrResponseError
)

// tcpStreamFactory TCP流工厂，实现了 tcpassembly.StreamFactory 接口
type tcpStreamFactory struct {
	m        sync.Map
	wg       sync.WaitGroup
	notifier Notifier
	Verbose  bool
}

// tcpStream TCP流处理器
type tcpStream struct {
	tcpreader.ReaderStream

	id                string             // net和transport的连接关系的友好显示
	net, transport    gopacket.Flow      // 网络层和传输层的流
	isFirstPkt        bool               // 是否收到了第一个有数据的包
	requestOrResponse RequestOrResponse  // 类型，区分是request还是response
	factory           *tcpStreamFactory  // 创建工厂
	state             *tcpState          // 两端共享的状态
	closeOnce         sync.Once          // 确保ReassemblyComplete只被调用一次
	Verbose           bool               // 是否打印详细信息
	httpHandler       HttpHandler        // HTTP处理器接口
}

// HttpHandler HTTP处理器接口
type HttpHandler interface {
	HandleRequest(buf *bufio.Reader, stream *tcpStream) error
	HandleResponse(buf *bufio.Reader, stream *tcpStream) error
}

// ReassemblyComplete implements tcpassembly.Stream's ReassemblyComplete function.
// 使用 sync.Once 来确保底层的 ReaderStream.ReassemblyComplete 只被调用一次。
func (r *tcpStream) ReassemblyComplete() {
	r.closeOnce.Do(func() {
		r.ReaderStream.ReassemblyComplete()
	})
}

// Reassembled implements tcpassembly.Stream's Reassembled function.
func (r *tcpStream) Reassembled(reassembly []tcpassembly.Reassembly) {
	if r.state.discard.Load() {
		return
	}

	// 检查第一个包
	if r.isFirstPkt && len(reassembly) > 0 && len(reassembly[0].Bytes) > 0 {
		r.isFirstPkt = false
		firstData := reassembly[0].Bytes

		if bytes.HasPrefix(firstData, []byte("HTTP/")) {
			r.requestOrResponse = RequestOrResponseResponse
		} else if bytes.HasPrefix(firstData, []byte("GET ")) ||
			bytes.HasPrefix(firstData, []byte("HEAD ")) ||
			bytes.HasPrefix(firstData, []byte("POST ")) {
			r.requestOrResponse = RequestOrResponseRequest
		} else {
			r.requestOrResponse = RequestOrResponseError
			// 不再进行处理
			if r.Verbose {
				log.Println("not http protocol:", r.id)
			}
			r.state.discard.Store(true)
			r.ReassemblyComplete()
			return
		}
	}
	r.ReaderStream.Reassembled(reassembly)
}

// GetState 获取TCP状态
func (s *tcpStream) GetState() *tcpState {
	return s.state
}

// GetNetTransport 获取网络和传输层信息
func (s *tcpStream) GetNetTransport() (gopacket.Flow, gopacket.Flow) {
	return s.net, s.transport
}

// GetNotifier 获取通知器
func (s *tcpStream) GetNotifier() Notifier {
	return s.factory.notifier
}

// run TCP流处理主循环
func (s *tcpStream) run() {
	defer func() {
		if s.Verbose {
			log.Println("out:", s.id)
		}
		s.factory.wg.Done()
	}()

	buf := bufio.NewReader(s)
	_, err := buf.Peek(1) // 必须读一次，确保s.requestOrResponse被设置

	if err != nil {
		if errors.Is(err, io.EOF) || errors.Is(err, io.ErrUnexpectedEOF) {
			return
		}
		log.Println("buf peek failed:", err)
		return
	}

_out:
	for {
		switch s.requestOrResponse {
		case RequestOrResponseRequest:
			if s.httpHandler != nil {
				err := s.httpHandler.HandleRequest(buf, s)
				if err != nil {
					if errors.Is(err, io.EOF) || errors.Is(err, io.ErrUnexpectedEOF) {
						return
					}
					log.Println("request buf read failed:", err)
					break _out
				}
			}
		case RequestOrResponseResponse:
			if s.httpHandler != nil {
				err := s.httpHandler.HandleResponse(buf, s)
				if err != nil {
					if errors.Is(err, io.EOF) || errors.Is(err, io.ErrUnexpectedEOF) {
						return
					}
					log.Println("response buf read failed:", err)
					break _out
				}
			}
		default:
			s.ReassemblyComplete()
		}
	}
}

// createConnectionKey 创建一个不区分流向的唯一连接标识
func createConnectionKey(netFlow, tcpFlow gopacket.Flow) string {
	srcIP, dstIP := netFlow.Endpoints()
	srcPort, dstPort := tcpFlow.Endpoints()
	if srcIP.String() > dstIP.String() || (srcIP.String() == dstIP.String() && srcPort.String() > dstPort.String()) {
		srcIP, dstIP = dstIP, srcIP
		srcPort, dstPort = dstPort, srcPort
	}
	return fmt.Sprintf("%s:%s-%s:%s", srcIP, srcPort, dstIP, dstPort)
}

// getTcpStream 获取TCP流实例
func (f *tcpStreamFactory) getTcpStream(net, transport gopacket.Flow, httpHandler HttpHandler) *tcpStream {
	id := createConnectionKey(net, transport)

	// 设置共享状态
	state, loaded := f.m.LoadOrStore(net.String()+":"+transport.String(), &tcpState{})
	f.m.LoadOrStore(net.Reverse().String()+":"+transport.Reverse().String(), state)

	if !loaded {
		f.notifier.OnTcpSession(id, net, transport)
	}

	return &tcpStream{
		id:           net.String() + ":" + transport.String(),
		ReaderStream: tcpreader.NewReaderStream(),
		factory:      f,
		net:          net,
		transport:    transport,
		state:        state.(*tcpState),
		isFirstPkt:   true,
		Verbose:      f.Verbose,
		httpHandler:  httpHandler,
	}
}

// New 方法在检测到新的 TCP 流时被调用
func (f *tcpStreamFactory) New(net, transport gopacket.Flow) tcpassembly.Stream {
	// 创建HTTP处理器
	httpHandler := &defaultHttpHandler{}
	
	s := f.getTcpStream(net, transport, httpHandler)
	f.wg.Add(1)
	go s.run()

	return s
}
